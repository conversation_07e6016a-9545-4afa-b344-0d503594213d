# Email Analysis System

A full-stack application that automatically identifies email receiving chains and ESP (Email Service Provider) types from incoming emails.

## 🚀 Features

- **Automatic Email Processing**: Receives and analyzes incoming emails
- **Receiving Chain Analysis**: Extracts and visualizes the path emails take through servers
- **ESP Detection**: Identifies the email service provider used to send emails
- **Responsive UI**: Clean, mobile-friendly interface for viewing results
- **Real-time Updates**: Live processing and display of email analysis

## 🏗️ Architecture

```
email-analysis-system/
├── backend/          # NestJS backend application
├── frontend/         # Next.js frontend application
├── docs/            # Documentation
└── README.md        # This file
```

## 🛠️ Tech Stack

- **Frontend**: Next.js with React
- **Backend**: Node.js with NestJS framework
- **Database**: MongoDB
- **Email Processing**: Mailgun/SendGrid webhooks
- **Deployment**: Vercel (frontend), Railway/Render (backend)

## 📋 Prerequisites

- Node.js (v18 or higher)
- MongoDB (local or cloud instance)
- Email service provider account (Mailgun/SendGrid)

## 🚀 Quick Start

### Backend Setup

```bash
cd backend
npm install
cp .env.example .env
# Configure your environment variables
npm run start:dev
```

### Frontend Setup

```bash
cd frontend
npm install
cp .env.local.example .env.local
# Configure your environment variables
npm run dev
```

## 📖 Documentation

- [API Documentation](./docs/api.md)
- [Email Header Analysis](./docs/email-analysis.md)
- [Deployment Guide](./docs/deployment.md)

## 🧪 Testing

Send test emails to the generated email address with the specified subject line to see the system in action.

## 📝 License

MIT License - see LICENSE file for details.
